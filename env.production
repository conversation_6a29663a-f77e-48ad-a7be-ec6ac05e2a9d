# API 配置
DEBUG=False
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
SUPABASE_URL=https://efhtgyfhwaxqequunbhc.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVmaHRneWZod2F4cWVxdXVuYmhjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzk0MzMxMTMsImV4cCI6MjA1NTAwOTExM30.5jsufdamloTbAO19QPEk7MhqdTdUSFooS13hdZSruyA

# 前端配置
NEXT_PUBLIC_API_URL=http://localhost:8000
NODE_ENV=production

# 可选：数据库配置（如果使用本地数据库）
# POSTGRES_DB=tianluo
# POSTGRES_USER=tianluo
# POSTGRES_PASSWORD=your_secure_password_here

# 可选：Redis 配置
# REDIS_URL=redis://redis:6379

# SSL 配置（如果使用 HTTPS）
# SSL_CERT_PATH=/path/to/cert.pem
# SSL_KEY_PATH=/path/to/key.pem