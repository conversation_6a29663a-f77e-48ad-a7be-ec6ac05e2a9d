'use client'

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { Eye, EyeOff } from "lucide-react"

interface LoginDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onLoginSuccess?: () => void
}

export function LoginDialog({ open, onOpenChange, onLoginSuccess }: LoginDialogProps) {
  const router = useRouter()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState({
    email: "",
    password: ""
  })

  const handleLogin = async () => {
    // 重置错误
    setErrors({
      email: "",
      password: ""
    })

    // 验证表单
    let hasError = false
    if (!email) {
      setErrors(prev => ({ ...prev, email: "请输入邮箱" }))
      hasError = true
    }
    if (!password) {
      setErrors(prev => ({ ...prev, password: "请输入密码" }))
      hasError = true
    }

    if (hasError) return

    try {
      setLoading(true)
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.detail || '登录失败')
      }

      // 存储token和用户信息
      localStorage.setItem('token', data.session.access_token)
      localStorage.setItem('user', JSON.stringify(data.user))

      // 关闭登录弹窗
      onOpenChange(false)
      
      // 登录成功提示
      toast.success("登录成功！")
      
      // 调用登录成功回调
      onLoginSuccess?.()
      
      // 刷新页面
      router.refresh()

    } catch (error: Error | unknown) {
      setErrors(prev => ({ ...prev, email: error instanceof Error ? error.message : '登录失败' }))
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-center text-2xl font-bold">登录账号</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="email">邮箱</Label>
            <Input 
              id="email" 
              type="email" 
              placeholder="请输入邮箱" 
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className={errors.email ? "border-red-500" : ""}
            />
            {errors.email && (
              <p className="text-sm text-red-500 mt-1">{errors.email}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="password">密码</Label>
            <div className="relative">
              <Input 
                id="password" 
                type={showPassword ? "text" : "password"}
                placeholder="请输入密码" 
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className={errors.password ? "border-red-500" : ""}
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <Eye className="h-4 w-4" />
                ) : (
                  <EyeOff className="h-4 w-4" />
                )}
              </Button>
            </div>
            {errors.password && (
              <p className="text-sm text-red-500 mt-1">{errors.password}</p>
            )}
          </div>
          <Button 
            className="w-full bg-blue-500 hover:bg-blue-600"
            onClick={handleLogin}
            disabled={loading}
          >
            {loading ? '登录中...' : '登录'}
          </Button>
          <div className="text-sm text-center space-x-4">
            <Link 
              href="/register" 
              className="text-blue-500 hover:text-blue-600"
              onClick={() => onOpenChange(false)}
            >
              注册账号
            </Link>
            <span className="text-gray-500">|</span>
            <Link 
              href="/forgot-password" 
              className="text-blue-500 hover:text-blue-600"
              onClick={() => onOpenChange(false)}
            >
              忘记密码
            </Link>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
} 