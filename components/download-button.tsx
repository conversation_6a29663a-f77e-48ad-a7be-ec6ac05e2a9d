'use client'

import { Button } from "@/components/ui/button"
import { useState } from 'react'
import { getDownloadUrl } from '@/lib/constants'

interface DownloadButtonProps {
  className?: string;
}

export function DownloadButton({ className }: DownloadButtonProps) {
  const [isDownloading, setIsDownloading] = useState(false);

  const handleDownload = async () => {
    try {
      setIsDownloading(true);

      // 获取当前平台
      // const platform = navigator.platform.toLowerCase().includes('win') ? 'windows' : 'macos';

      // 动态获取下载链接
      const downloadUrl = await getDownloadUrl();


      // 创建下载链接
      const link = document.createElement('a');
      // todo：当前仅支持windows
      // link.href = `${process.env.NEXT_PUBLIC_API_URL}/api/client/latest-file/${platform}`;
      link.href = downloadUrl;
      link.download = ''; // 让浏览器自动处理文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

    } catch (error) {
      console.error('下载出错:', error);
      alert('下载失败，请稍后重试');
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <Button
      onClick={handleDownload}
      disabled={isDownloading}
      className={className}
    >
      {isDownloading ? '准备下载...' : '免费下载'}
    </Button>
  );
}