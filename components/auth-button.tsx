'use client'

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { LoginDialog } from "@/components/login-dialog"
import { toast } from "sonner"
import { useRouter } from "next/navigation"

export function AuthButton() {
  const router = useRouter()
  const [isLoginDialogOpen, setIsLoginDialogOpen] = useState(false)
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [userName, setUserName] = useState("")

  useEffect(() => {
    // 检查登录状态
    const user = localStorage.getItem('user')
    if (user) {
      const userData = JSON.parse(user)
      setIsLoggedIn(true)
      setUserName(userData.email)
    }
  }, [])

  const handleLogout = async () => {
    try {
      // 调用后端登出接口
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/logout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
      })

      if (!response.ok) {
        throw new Error('登出失败')
      }

      // 清除本地存储的用户信息
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      
      // 更新状态
      setIsLoggedIn(false)
      setUserName("")
      
      // 显示提示
      toast.success("已退出登录")
      
      // 刷新页面
      router.refresh()
    } catch (error: Error | unknown) {
      toast.error(error instanceof Error ? error.message : '登出失败')
    }
  }

  return (
    <>
      {isLoggedIn ? (
        <div className="flex items-center gap-4">
          <span className="text-sm text-gray-600">{userName}</span>
          <Button 
            variant="outline" 
            onClick={handleLogout}
            className="bg-gray-300 text-black hover:bg-gray-400"
          >
            登出
          </Button>
        </div>
      ) : (
        <div className="flex items-center gap-4">
          <Button 
            variant="default" 
            onClick={() => setIsLoginDialogOpen(true)}
            className="bg-blue-500 hover:bg-blue-600"
          >
            登录
          </Button>
          <Button
            variant="outline"
            onClick={() => router.push('/register')}
            className="border-blue-500 text-blue-500 hover:bg-blue-50"
          >
            注册
          </Button>
        </div>
      )}

      <LoginDialog 
        open={isLoginDialogOpen} 
        onOpenChange={setIsLoginDialogOpen}
        onLoginSuccess={() => {
          setIsLoggedIn(true)
          const user = localStorage.getItem('user')
          if (user) {
            setUserName(JSON.parse(user).email)
          }
        }}
      />
    </>
  )
} 