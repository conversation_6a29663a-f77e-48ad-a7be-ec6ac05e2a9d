'use client'

import Link from 'next/link'
import Image from 'next/image'
import { DownloadButton } from "@/components/download-button"

export function BlockPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <main className="flex-1 flex flex-col items-center">
        <section className="w-full py-12 md:py-24 lg:py-32 xl:py-48 bg-gray-100">
          <div className="container px-4 md:px-6 mx-auto">
            <div className="flex flex-col items-center space-y-4 text-center">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none">
                  告别繁琐，字幕轻松搞定
                </h1>
                <p className="mx-auto max-w-[700px] text-gray-500 md:text-xl dark:text-gray-400">
                安全、先进的AI字幕技术，几分钟内本地化您的视频。
                </p>
              </div>
              <div className="space-y-4 w-full max-w-md">
                <DownloadButton className="w-full bg-blue-500 hover:bg-blue-600 text-white text-lg px-8 py-6 rounded-lg font-semibold transform hover:scale-105 transition-transform duration-200" />
              </div>
            </div>
          </div>
        </section>
        <section className="w-full py-12">
          <div className="container px-4 md:px-6 mx-auto">
            <div className="grid md:grid-cols-2 gap-12 items-center mb-24">
              <div className="space-y-4">
                <h2 className="text-3xl font-bold">自动识别加字幕</h2>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <span className="mr-2">✓</span>
                    多语言识别，识别准确度接近人类水平，支持 50+ 语言
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">✓</span>
                    本地识别永久免费，为创作加速
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">✓</span>
                    极速处理，1小时视频加字幕，最快5分钟完成
                  </li>
                </ul>
              </div>
              <div className="relative">
                <Image
                    src="/主页面.png"
                    alt="Audio transcription"
                  width={600}
                  height={400}
                  className="rounded-lg shadow-lg"
                />
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-12 items-center mb-24">
              <div className="relative order-2 md:order-1">
                <Image
                  src="/翻译.png"
                  alt="Video summary interface"
                  width={600}
                  height={400}
                  className="rounded-lg shadow-lg"
                />
              </div>
              <div className="space-y-4 order-1 md:order-2">
                <h2 className="text-3xl font-bold">自动翻译字幕</h2>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <span className="mr-2">✓</span>
                    支持翻译50总语言，支持转成多种格式输出
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">✓</span>
                    AI翻译，自定义提示词，让内容更专业
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">✓</span>
                    同步支持千问，Kimi，智谱AI多种翻译
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>
        <section className="w-full py-12 bg-gray-100" id="faq">
          <div className="container px-4 md:px-6 mx-auto">
            <h2 className="text-3xl font-bold text-center mb-8">常见问题</h2>
            <div className="max-w-3xl mx-auto space-y-4">
              <div className="p-4 rounded-lg bg-blue-50">
                <h3 className="font-medium mb-2">软件可以免费使用吗？</h3>
                <p className="text-gray-600">软件大部分功能都可以免费无限制使用，你完全可以通过软件免费制作一份高质量的双语字幕。</p>
              </div>
              
              <div className="p-4 rounded-lg bg-blue-50">
                <h3 className="font-medium mb-2">会上传视频到服务器吗？</h3>
                <p className="text-gray-600">使用本地识别，你的媒体文件并不会上传到我们的服务器。使用云端识别，我们会先提取出音频，然后上传服务器识别，任务完成后会删除服务器端的资源。</p>
              </div>
              
              <div className="p-4 rounded-lg bg-blue-50">
                <h3 className="font-medium mb-2">本地设备没有显卡或者显卡配置很低可以使用吗？</h3>
                <p className="text-gray-600">叠影使用先进的AI算法，即使在低配显卡的设备上也可以快速生成字幕。</p>
              </div>
              
              <div className="p-4 rounded-lg bg-blue-50">
                <h3 className="font-medium mb-2">LLM支持自己的key吗？</h3>
                <p className="text-gray-600">支持的，如果你有自己的key，我们不会收取算力。</p>
              </div>
              
              <div className="p-4 rounded-lg bg-blue-50">
                <h3 className="font-medium mb-2">可以批量生成字幕和翻译吗？</h3>
                <p className="text-gray-600">支持批量识别任务。</p>
              </div>
              
              <div className="p-4 rounded-lg bg-blue-50">
                <h3 className="font-medium mb-2">使用叠影翻译视频需要多长时间？</h3>
                <p className="text-gray-600">一小时的视频最快5分钟翻译完成。</p>
              </div>
              
              <div className="p-4 rounded-lg bg-blue-50">
                <h3 className="font-medium mb-2">支持哪些平台使用？</h3>
                <p className="text-gray-600">目前我们支持在 windows系统中使用，mac os 版即将推出。</p>
              </div>
            </div>
          </div>
        </section>
      </main>
      <footer className="flex flex-col gap-2 sm:flex-row py-6 w-full shrink-0 items-center px-4 md:px-6 border-t">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 py-8">
            <div className="space-y-4">
              <div className="flex items-center">
                <Image 
                  src="/lapped.png"
                  alt="叠影 Logo"
                  width={40}
                  height={40}
                />
                <span className="ml-2 text-xl font-bold">叠影</span>
              </div>
              <p className="text-sm text-gray-600">现在就开始为您的视频添加你想要的字幕，人们正等待您的内容.</p>

            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-4">LINKS</h3>
              <ul className="space-y-2">
                <li><Link href="#" className="text-gray-600 hover:text-gray-900">使用教程</Link></li>
                <li><Link href="#faq" className="text-gray-600 hover:text-gray-900">常见问题</Link></li>
                <li><Link href="#" className="text-gray-600 hover:text-gray-900">意见反馈</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-4">联系我们</h3>
              <ul className="space-y-2">
                <p className="text-gray-600 hover:text-gray-900">邮件联系 <EMAIL></p>
              </ul>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}