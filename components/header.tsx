'use client'

import { Logo } from "./logo"
import { AuthButton } from "./auth-button"
import Link from "next/link"
import { getDownloadUrl } from '@/lib/constants'

export function Header() {
  const handleDownload = async (e: React.MouseEvent) => {
    e.preventDefault();
    try {
      // 动态获取下载链接
      const downloadUrl = await getDownloadUrl();

      // 创建下载链接
      const link = document.createElement('a');
      // todo：当前仅支持windows
      link.href = downloadUrl;
      link.download = ''; // 让浏览器自动处理文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('下载出错:', error);
      alert('下载失败，请稍后重试');
    }
  };

  return (
    <header className="fixed top-0 left-0 right-0 px-4 lg:px-6 h-20 flex items-center bg-white z-50 border-b shadow-sm">
      <Logo />
      <nav className="ml-auto flex gap-4 sm:gap-6 items-center">
        <Link className="text-sm font-medium hover:underline underline-offset-4" href="/docs">
          使用教程
        </Link>
        <Link className="text-sm font-medium hover:underline underline-offset-4" href="/faq">
          常见问题
        </Link>
        <Link
          className="text-sm font-medium hover:underline underline-offset-4"
          href="/download"
          onClick={handleDownload}
        >
          免费下载
        </Link>
        <AuthButton />
      </nav>
    </header>
  )
}