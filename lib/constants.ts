// 下载相关常量

/**
 * 动态获取下载链接
 * 从后端API获取24小时有效期的下载链接
 */
export async function getDownloadUrl(): Promise<string> {

  // return '//lapped-files.oss-cn-beijing.aliyuncs.com/client/Lapped%20AI%20setup.exe?x-oss-signature-version=OSS4-HMAC-SHA256&x-oss-date=20250606T074136Z&x-oss-expires=900&x-oss-credential=LTAI5tF4QU3nvf7zcfzydEmc%2F20250606%2Fcn-beijing%2Foss%2Faliyun_v4_request&x-oss-signature=8a68fabf2bc8380f6fd54f108976b952930c5e94738280decb328994ea8641c2';
  const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/client/link`);
  // const response = await fetch(`http://127.0.0.1:8000/api/client/link`);

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();

  // 根据实际API响应格式解析数据
  if (data.code === 200 && data.message === "success" && data.data && data.data.url) {
    return data.data.url;
    // return '//lapped-files.oss-cn-beijing.aliyuncs.com/client/Lapped%20AI%20setup.exe?x-oss-signature-version=OSS4-HMAC-SHA256&x-oss-date=20250606T074136Z&x-oss-expires=900&x-oss-credential=LTAI5tF4QU3nvf7zcfzydEmc%2F20250606%2Fcn-beijing%2Foss%2Faliyun_v4_request&x-oss-signature=8a68fabf2bc8380f6fd54f108976b952930c5e94738280decb328994ea8641c2';
  }
  else {
    throw new Error('获取下载链接失败');
  }
}

// 其他常量可以在这里添加
export const APP_NAME = 'Lapped AI';
export const SUPPORT_EMAIL = '<EMAIL>';