import axios from 'axios'
import { tokenManager } from './supabase'

const request = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1',
  timeout: 15000
})

// 请求拦截器
request.interceptors.request.use(
  async (config) => {
    // 如果是登录或注册请求，不需要添加token
    if (config.url?.includes('/auth/login') || 
        config.url?.includes('/auth/signup') || 
        config.url?.includes('/auth/reset-password') ||
        config.url?.includes('/auth/confirm-password-reset')) {
      return config
    }

    const session = await tokenManager.getSession()
    if (session?.access_token) {
      config.headers.Authorization = `Bearer ${session.access_token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => response.data,
  async (error) => {
    const originalRequest = error.config

    // 如果是 401 错误且未尝试过刷新 token
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        // 尝试刷新 token
        const session = await tokenManager.refreshToken()
        if (session?.access_token) {
          // 使用新 token 重试请求
          originalRequest.headers.Authorization = `Bearer ${session.access_token}`
          return request(originalRequest)
        }
      } catch {
        // token 刷新失败，清除会话并跳转到登录页
        tokenManager.clearSession()
        window.location.href = '/login'
      }
    }

    return Promise.reject(error)
  }
)

export default request 