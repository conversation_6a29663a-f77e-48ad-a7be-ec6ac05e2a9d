import { createClient } from '@supabase/supabase-js'
import { Session } from '@supabase/supabase-js'

// 检查必要的环境变量
if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
  throw new Error('NEXT_PUBLIC_SUPABASE_URL is required')
}

if (!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
  throw new Error('NEXT_PUBLIC_SUPABASE_ANON_KEY is required')
}

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// 创建 Supabase 客户端
export const supabase = createClient(supabaseUrl, supabaseKey)

// Token 管理
export const tokenManager = {
  // 保存会话信息
  setSession: async (session: Session | null) => {
    if (session) {
      localStorage.setItem('sb-access-token', session.access_token)
      localStorage.setItem('sb-refresh-token', session.refresh_token)
      // 设置 supabase 客户端的 session
      await supabase.auth.setSession({
        access_token: session.access_token,
        refresh_token: session.refresh_token,
      })
    }
  },

  // 获取当前 session
  getSession: async () => {
    const { data: { session }, error } = await supabase.auth.getSession()
    if (error) {
      console.error('获取session失败:', error.message)
      return null
    }
    return session
  },

  // 刷新 token
  refreshToken: async () => {
    const { data: { session }, error } = await supabase.auth.refreshSession()
    if (error) {
      console.error('刷新token失败:', error.message)
      return null
    }
    if (session) {
      await tokenManager.setSession(session)
    }
    return session
  },

  // 清除 token
  clearSession: () => {
    localStorage.removeItem('sb-access-token')
    localStorage.removeItem('sb-refresh-token')
    supabase.auth.signOut()
  }
} 