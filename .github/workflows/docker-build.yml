name: 构建并推送Docker镜像

on:
  push:
    branches: [ "main", "master","build_0.2.1"]
    tags: [ 'v*' ]
  pull_request:
    branches: [ "main", "master","build_0.2.1"]

env:
  # 设置镜像仓库
  REGISTRY: ghcr.io
  # GitHub仓库名称作为镜像名称
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    # 设置权限
    permissions:
      contents: read
      packages: write

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 登录到GitHub容器仓库
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 提取元数据
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,format=short

      # 构建并推送Docker镜像
      - name: 构建并推送Docker镜像
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: |
            type=gha
            type=registry,ref=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:buildcache
          cache-to: |
            type=gha,mode=max
            type=registry,ref=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:buildcache,mode=max
          build-args: |
            NODE_ENV=production
            NEXT_TELEMETRY_DISABLED=1

      - name: 输出镜像信息
        if: github.event_name != 'pull_request'
        run: |
          echo "镜像构建完成并已推送到GitHub Container Registry"
          echo "镜像地址: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest"
          echo "访问镜像: https://github.com/${{ github.repository }}/pkgs/container"
