# Docker部署指南

本文档提供了使用Docker部署天罗项目的详细步骤。

## 部署架构

项目采用以下架构进行部署：

```
客户端 -> Nginx (80/443端口) -> 前端应用 (3000端口) / 后端API (8000端口)
```

- **Nginx**: 作为反向代理，处理请求路由和SSL终止
- **前端应用**: Next.js应用，提供用户界面
- **后端API**: FastAPI应用，提供API服务

## 部署步骤

### 1. 准备环境

确保服务器上已安装Docker和Docker Compose：

```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.24.6/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 克隆项目代码

```bash
git clone <项目仓库URL>
cd <项目目录>
```

### 3. 配置环境变量

项目已包含`.env`文件，其中包含了必要的环境变量。如果需要，可以根据实际情况修改这些变量。

### 4. 构建和启动服务

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

### 5. 验证部署

访问服务器IP地址或域名，确认应用是否正常运行：

- 前端应用: http://your-server-ip/
- API服务: http://your-server-ip/api/

## 服务管理

### 查看日志

```bash
# 查看所有服务的日志
docker-compose logs

# 查看特定服务的日志
docker-compose logs nextjs
docker-compose logs api
docker-compose logs nginx
```

### 重启服务

```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart nextjs
docker-compose restart api
docker-compose restart nginx
```

### 停止服务

```bash
# 停止所有服务
docker-compose down

# 停止并删除卷（慎用）
docker-compose down -v
```

### 更新部署

当有代码更新时，可以按照以下步骤更新部署：

```bash
# 拉取最新代码
git pull

# 重新构建镜像
docker-compose build

# 重启服务
docker-compose up -d
```

## 配置HTTPS（可选）

如果需要配置HTTPS，请按照以下步骤操作：

1. 获取SSL证书（可以使用Let's Encrypt）
2. 创建`ssl`目录并将证书文件放入其中
3. 修改`nginx/default.conf`文件，添加HTTPS配置
4. 取消注释`docker-compose.yml`中的SSL卷挂载
5. 重启Nginx服务

## 故障排除

### 1. 服务无法启动

检查Docker日志以获取详细错误信息：

```bash
docker-compose logs
```

### 2. 无法访问前端或API

检查Nginx配置和日志：

```bash
docker-compose logs nginx
```

### 3. 数据库连接问题

确认环境变量中的Supabase URL和密钥是否正确：

```bash
docker-compose config
```

## 安全建议

1. 不要在公共仓库中存储敏感信息（如API密钥）
2. 定期更新Docker镜像和依赖
3. 使用非root用户运行容器
4. 配置防火墙，只开放必要的端口
5. 启用HTTPS，保护数据传输安全
