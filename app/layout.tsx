import type { Metadata } from "next";
import localFont from "next/font/local";
import "./globals.css";
import { Toaster } from 'sonner'
import { Header } from "@/components/header"
import { AuthProvider } from '@/contexts/auth-context'

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export const metadata: Metadata = {
  title: "Lapped AI官方网站",
  description: "Lapped AI是一款智能写作助手，帮助你更高效地创作内容",
  icons: {
    icon: '/lapped.ico',
    apple: '/lapped.ico',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <AuthProvider>
          <Header />
          <main className="mt-20">
            {children}
          </main>
        </AuthProvider>
        <Toaster position="top-center" />
      </body>
    </html>
  );
}
