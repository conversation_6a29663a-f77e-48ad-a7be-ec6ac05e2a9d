'use client'

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import Link from "next/link"
import { useState } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { Logo } from "@/components/logo"

export default function RegisterPage() {
  const router = useRouter()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState({
    email: "",
    password: "",
    confirmPassword: ""
  })

  const handleRegister = async () => {
    // 重置所有错误
    setErrors({
      email: "",
      password: "",
      confirmPassword: ""
    })

    // 验证表单
    let hasError = false
    if (!email) {
      setErrors(prev => ({ ...prev, email: "请输入邮箱" }))
      hasError = true
    }
    if (!password) {
      setErrors(prev => ({ ...prev, password: "请输入密码" }))
      hasError = true
    }
    if (!confirmPassword) {
      setErrors(prev => ({ ...prev, confirmPassword: "请确认密码" }))
      hasError = true
    }
    if (password !== confirmPassword) {
      setErrors(prev => ({ ...prev, confirmPassword: "两次输入的密码不一致" }))
      hasError = true
    }

    if (hasError) return

    try {
      setLoading(true)
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/signup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
        }),
      })

      if (!response.ok) {
        throw new Error('注册失败')
      }

      toast.success("注册成功！请查收邮件完成验证")
      router.push('/')
    } catch (error: Error | unknown) {
      setErrors(prev => ({ ...prev, email: error instanceof Error ? error.message : '注册失败' }))
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto flex h-screen">
      {/* 左侧营销内容 */}
      <div className="hidden lg:flex w-1/2 flex-col justify-center p-12 bg-gray-50">
        <div className="flex items-center mb-12">
          <Logo />
        </div>
        <div className="space-y-6 max-w-md">
          <h1 className="text-4xl font-bold leading-tight">
            立即免费注册并获得您的第一个视频翻译。
          </h1>
          <p className="text-xl text-gray-600">
            简单、快速、直接。
          </p>
        </div>
      </div>

      {/* 右侧注册表单 */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
        <div className="w-full max-w-[400px] space-y-6">
          <div className="space-y-2 text-center">
            <h1 className="text-2xl font-bold">创建账号</h1>
            <p className="text-gray-500">请填写以下信息完成注册</p>
          </div>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">邮箱</Label>
              <Input 
                id="email" 
                type="email" 
                placeholder="请输入邮箱" 
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className={errors.email ? "border-red-500" : ""}
              />
              {errors.email && (
                <p className="text-sm text-red-500 mt-1">{errors.email}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">密码</Label>
              <Input 
                id="password" 
                type="password" 
                placeholder="请输入密码" 
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className={errors.password ? "border-red-500" : ""}
              />
              {errors.password && (
                <p className="text-sm text-red-500 mt-1">{errors.password}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirm-password">确认密码</Label>
              <Input 
                id="confirm-password" 
                type="password" 
                placeholder="请再次输入密码" 
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className={errors.confirmPassword ? "border-red-500" : ""}
              />
              {errors.confirmPassword && (
                <p className="text-sm text-red-500 mt-1">{errors.confirmPassword}</p>
              )}
            </div>
            <Button 
              className="w-full bg-blue-500 hover:bg-blue-600"
              onClick={handleRegister}
              disabled={loading}
            >
              {loading ? '注册中...' : '注册'}
            </Button>
          </div>
          <div className="text-sm text-center text-gray-500">
            已有账号？
            <Link href="/" className="text-blue-500 hover:text-blue-600 ml-1">
              返回登录
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
} 