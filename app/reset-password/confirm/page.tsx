'use client'

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useState } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"

export default function ResetPasswordConfirmPage() {
  const router = useRouter()
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [loading, setLoading] = useState(false)

  const handleResetPassword = async () => {
    if (!password || !confirmPassword) {
      toast.error("请填写所有字段")
      return
    }

    if (password !== confirmPassword) {
      toast.error("两次输入的密码不一致")
      return
    }

    try {
      setLoading(true)
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/update-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          new_password: password,
        }),
      })

      if (!response.ok) {
        throw new Error('重置密码失败')
      }

      toast.success("密码重置成功！")
      router.push('/')
    } catch (error: Error | unknown) {
      toast.error(error instanceof Error ? error.message : '重置密码失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container max-w-[400px] mx-auto pt-20">
      <div className="space-y-6">
        <div className="space-y-2 text-center">
          <h1 className="text-2xl font-bold">设置新密码</h1>
          <p className="text-gray-500">请输入您的新密码</p>
        </div>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="password">新密码</Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="请输入新密码"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="confirm-password">确认密码</Label>
            <Input
              id="confirm-password"
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              placeholder="请再次输入新密码"
            />
          </div>
          <Button
            className="w-full bg-blue-500 hover:bg-blue-600"
            onClick={handleResetPassword}
            disabled={loading}
          >
            {loading ? '提交中...' : '确认修改'}
          </Button>
        </div>
      </div>
    </div>
  )
} 