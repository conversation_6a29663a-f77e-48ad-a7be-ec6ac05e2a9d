'use client'

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import Link from "next/link"
import { useState, useEffect } from "react"
import { toast } from "sonner"
import { useRouter } from "next/navigation"
import { Logo } from "@/components/logo"

export default function ForgotPasswordPage() {
  const router = useRouter()
  const [email, setEmail] = useState("")
  const [verificationCode, setVerificationCode] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [loading, setLoading] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [errors, setErrors] = useState({
    email: "",
    verificationCode: "",
    newPassword: "",
    confirmPassword: ""
  })

  useEffect(() => {
    let timer: NodeJS.Timeout
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1)
      }, 1000)
    }
    return () => {
      if (timer) {
        clearTimeout(timer)
      }
    }
  }, [countdown])

  const handleSendVerification = async () => {
    // 重置错误
    setErrors({
      email: "",
      verificationCode: "",
      newPassword: "",
      confirmPassword: ""
    })

    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      setErrors(prev => ({ ...prev, email: "请输入有效的邮箱地址" }))
      return
    }

    setCountdown(60)

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      if (!response.ok) {
        throw new Error('发送验证码失败')
      }

      toast.success("验证码已发送到您的邮箱")
    } catch (error: Error | unknown) {
      setErrors(prev => ({ ...prev, email: error instanceof Error ? error.message : '发送验证码失败' }))
      setCountdown(0)
    }
  }

  const handleResetPassword = async () => {
    // 重置错误
    setErrors({
      email: "",
      verificationCode: "",
      newPassword: "",
      confirmPassword: ""
    })

    let hasError = false
    if (!email) {
      setErrors(prev => ({ ...prev, email: "请输入邮箱" }))
      hasError = true
    }
    if (!verificationCode) {
      setErrors(prev => ({ ...prev, verificationCode: "请输入验证码" }))
      hasError = true
    }
    if (!newPassword) {
      setErrors(prev => ({ ...prev, newPassword: "请输入新密码" }))
      hasError = true
    }
    if (!confirmPassword) {
      setErrors(prev => ({ ...prev, confirmPassword: "请确认新密码" }))
      hasError = true
    }
    if (newPassword !== confirmPassword) {
      setErrors(prev => ({ ...prev, confirmPassword: "两次输入的密码不一致" }))
      hasError = true
    }
    if (newPassword && newPassword.length < 6) {
      setErrors(prev => ({ ...prev, newPassword: "密码长度不能少于6位" }))
      hasError = true
    }

    if (hasError) return

    try {
      setLoading(true)
      const resetResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          code: verificationCode,
          new_password: newPassword,
        }),
      })

      if (!resetResponse.ok) {
        const errorData = await resetResponse.json()
        throw new Error(errorData.detail || '重置密码失败')
      }

      toast.success("密码重置成功！")
      router.push('/')
    } catch (error: Error | unknown) {
      setErrors(prev => ({ ...prev, email: error instanceof Error ? error.message : '重置密码失败' }))
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto flex h-screen">
      {/* 左侧品牌展示 */}
      <div className="hidden lg:flex w-1/2 flex-col justify-center p-12 bg-gray-50">
        <div className="flex items-center mb-12">
          <Logo />
        </div>
        <div className="space-y-6 max-w-md">
          <h1 className="text-4xl font-bold leading-tight">
            找回密码
          </h1>
          <p className="text-xl text-gray-600">
            通过邮箱验证码重置您的密码。
          </p>
        </div>
      </div>

      {/* 右侧重置表单 */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
        <div className="w-full max-w-[400px] space-y-6">
          <div className="space-y-2 text-center">
            <h1 className="text-2xl font-bold">重置密码</h1>
            <p className="text-gray-500">请填写以下信息</p>
          </div>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">邮箱</Label>
              <Input 
                id="email" 
                type="email" 
                placeholder="请输入邮箱" 
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className={errors.email ? "border-red-500" : ""}
              />
              {errors.email && (
                <p className="text-sm text-red-500 mt-1">{errors.email}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="verification-code">验证码</Label>
              <div className="relative">
                <Input 
                  id="verification-code" 
                  type="text" 
                  placeholder="请输入验证码" 
                  className={`pr-[130px] ${errors.verificationCode ? "border-red-500" : ""}`}
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value)}
                />
                <Button 
                  variant="default"
                  className="absolute right-1 top-1/2 -translate-y-1/2 h-[32px] bg-blue-500 hover:bg-blue-600"
                  onClick={handleSendVerification}
                  disabled={countdown > 0}
                >
                  {countdown > 0 ? `${countdown}秒后重试` : '发送验证码'}
                </Button>
              </div>
              {errors.verificationCode && (
                <p className="text-sm text-red-500 mt-1">{errors.verificationCode}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-password">新密码</Label>
              <Input 
                id="new-password" 
                type="password" 
                placeholder="请输入新密码" 
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className={errors.newPassword ? "border-red-500" : ""}
              />
              {errors.newPassword && (
                <p className="text-sm text-red-500 mt-1">{errors.newPassword}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirm-password">确认密码</Label>
              <Input 
                id="confirm-password" 
                type="password" 
                placeholder="请再次输入新密码" 
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className={errors.confirmPassword ? "border-red-500" : ""}
              />
              {errors.confirmPassword && (
                <p className="text-sm text-red-500 mt-1">{errors.confirmPassword}</p>
              )}
            </div>
            <Button 
              className="w-full bg-blue-500 hover:bg-blue-600"
              onClick={handleResetPassword}
              disabled={loading}
            >
              {loading ? '提交中...' : '重置密码'}
            </Button>
          </div>
          <div className="text-sm text-center text-gray-500">
            想起密码了？
            <Link href="/" className="text-blue-500 hover:text-blue-600 ml-1">
              返回登录
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
} 